package com.qccconfluencemcp.test;

/**
 * 测试MCP API的可用性
 */
public class McpApiTest {
    
    public static void main(String[] args) {
        try {
            // 测试核心类是否可用
            System.out.println("测试MCP SDK API可用性...");
            
            // 检查是否可以访问MCP相关的类
            Class.forName("io.modelcontextprotocol.McpClient");
            System.out.println("✓ McpClient 类可用");
            
        } catch (ClassNotFoundException e) {
            System.out.println("✗ 找不到类: " + e.getMessage());

            // 尝试其他可能的类
            String[] possibleClasses = {
                "io.modelcontextprotocol.McpServer",
                "io.modelcontextprotocol.client.McpClient",
                "io.modelcontextprotocol.server.McpServer",
                "io.modelcontextprotocol.spec.McpSchema",
                "io.modelcontextprotocol.transport.Transport"
            };

            for (String className : possibleClasses) {
                try {
                    Class.forName(className);
                    System.out.println("✓ 找到类: " + className);
                } catch (ClassNotFoundException e2) {
                    System.out.println("✗ 找不到类: " + className);
                }
            }
        }
    }
}
