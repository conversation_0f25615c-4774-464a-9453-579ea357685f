package com.qccconfluencemcp.mcp;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qccconfluencemcp.model.ConfluencePage;
import com.qccconfluencemcp.model.ConfluenceSearchResult;
import com.qccconfluencemcp.service.ConfluenceClient;
import io.modelcontextprotocol.spec.McpSchema;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Confluence MCP工具定义
 */
@Component
public class ConfluenceTools {
    
    private final ConfluenceClient confluenceClient;
    private final ObjectMapper objectMapper;
    
    public ConfluenceTools(ConfluenceClient confluenceClient, ObjectMapper objectMapper) {
        this.confluenceClient = confluenceClient;
        this.objectMapper = objectMapper;
    }
    
    /**
     * 获取页面内容工具
     */
    public McpSchema.Tool getPageContentTool() {
        String schema = """
            {
              "type": "object",
              "properties": {
                "pageId": {
                  "type": "string",
                  "description": "Confluence页面ID"
                },
                "pageUrl": {
                  "type": "string",
                  "description": "Confluence页面URL（可选，如果提供则从URL中提取pageId）"
                }
              },
              "required": ["pageId"],
              "additionalProperties": false
            }
            """;
        
        return new McpSchema.Tool(
            "get_confluence_page",
            "获取Confluence页面的完整内容，包括标题、正文、空间信息等",
            schema
        );
    }
    
    /**
     * 搜索内容工具
     */
    public McpSchema.Tool searchContentTool() {
        String schema = """
            {
              "type": "object",
              "properties": {
                "query": {
                  "type": "string",
                  "description": "搜索关键词"
                },
                "limit": {
                  "type": "integer",
                  "description": "返回结果数量限制（默认10）",
                  "default": 10,
                  "minimum": 1,
                  "maximum": 50
                },
                "start": {
                  "type": "integer",
                  "description": "结果起始位置（默认0）",
                  "default": 0,
                  "minimum": 0
                }
              },
              "required": ["query"],
              "additionalProperties": false
            }
            """;
        
        return new McpSchema.Tool(
            "search_confluence",
            "在Confluence中搜索页面内容",
            schema
        );
    }
    
    /**
     * 处理获取页面内容的请求
     */
    public McpSchema.CallToolResult handleGetPageContent(Map<String, Object> arguments) {
        try {
            String pageId = (String) arguments.get("pageId");
            String pageUrl = (String) arguments.get("pageUrl");
            
            ConfluencePage page;
            if (pageUrl != null && !pageUrl.trim().isEmpty()) {
                page = confluenceClient.getPageByUrl(pageUrl).block();
            } else if (pageId != null && !pageId.trim().isEmpty()) {
                page = confluenceClient.getPageById(pageId).block();
            } else {
                return new McpSchema.CallToolResult("错误：必须提供pageId或pageUrl参数", true);
            }
            
            if (page == null) {
                return new McpSchema.CallToolResult("未找到指定的页面", true);
            }
            
            // 构建返回结果
            StringBuilder result = new StringBuilder();
            result.append("# ").append(page.getTitle()).append("\n\n");
            
            if (page.getSpace() != null) {
                result.append("**空间**: ").append(page.getSpace().getName()).append("\n");
                result.append("**空间键**: ").append(page.getSpace().getKey()).append("\n\n");
            }
            
            if (page.getVersion() != null) {
                result.append("**版本**: ").append(page.getVersion().getNumber()).append("\n");
                result.append("**更新时间**: ").append(page.getVersion().getWhen()).append("\n\n");
            }
            
            result.append("## 页面内容\n\n");
            
            // 优先使用view格式的内容（HTML），如果没有则使用storage格式
            if (page.getBody() != null) {
                if (page.getBody().getView() != null && page.getBody().getView().getValue() != null) {
                    result.append(page.getBody().getView().getValue());
                } else if (page.getBody().getStorage() != null && page.getBody().getStorage().getValue() != null) {
                    result.append(page.getBody().getStorage().getValue());
                } else {
                    result.append("页面内容为空");
                }
            } else {
                result.append("页面内容为空");
            }
            
            return new McpSchema.CallToolResult(result.toString(), false);
            
        } catch (Exception e) {
            return new McpSchema.CallToolResult("获取页面内容时发生错误: " + e.getMessage(), true);
        }
    }
    
    /**
     * 处理搜索内容的请求
     */
    public McpSchema.CallToolResult handleSearchContent(Map<String, Object> arguments) {
        try {
            String query = (String) arguments.get("query");
            Integer limit = arguments.get("limit") != null ? 
                ((Number) arguments.get("limit")).intValue() : 10;
            Integer start = arguments.get("start") != null ? 
                ((Number) arguments.get("start")).intValue() : 0;
            
            if (query == null || query.trim().isEmpty()) {
                return new McpSchema.CallToolResult("错误：搜索关键词不能为空", true);
            }
            
            ConfluenceSearchResult searchResult = confluenceClient.searchContent(query, limit, start).block();
            
            if (searchResult == null || searchResult.getResults() == null || searchResult.getResults().isEmpty()) {
                return new McpSchema.CallToolResult("未找到匹配的搜索结果", false);
            }
            
            StringBuilder result = new StringBuilder();
            result.append("# 搜索结果\n\n");
            result.append("**搜索关键词**: ").append(query).append("\n");
            result.append("**找到结果**: ").append(searchResult.getSize()).append(" 条\n\n");
            
            for (ConfluenceSearchResult.SearchResultItem item : searchResult.getResults()) {
                result.append("## ").append(item.getTitle()).append("\n");
                result.append("**页面ID**: ").append(item.getId()).append("\n");
                if (item.getSpace() != null) {
                    result.append("**空间**: ").append(item.getSpace().getName()).append("\n");
                }
                if (item.getUrl() != null) {
                    result.append("**链接**: ").append(item.getUrl()).append("\n");
                }
                if (item.getExcerpt() != null && !item.getExcerpt().trim().isEmpty()) {
                    result.append("**摘要**: ").append(item.getExcerpt()).append("\n");
                }
                result.append("\n---\n\n");
            }
            
            return new McpSchema.CallToolResult(result.toString(), false);
            
        } catch (Exception e) {
            return new McpSchema.CallToolResult("搜索时发生错误: " + e.getMessage(), true);
        }
    }
    
    /**
     * 获取所有工具
     */
    public List<McpSchema.Tool> getAllTools() {
        return List.of(
            getPageContentTool(),
            searchContentTool()
        );
    }
}
