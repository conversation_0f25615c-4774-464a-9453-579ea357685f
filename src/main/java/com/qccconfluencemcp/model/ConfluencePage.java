package com.qccconfluencemcp.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Confluence页面模型
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConfluencePage {
    
    private String id;
    private String type;
    private String status;
    private String title;
    private Space space;
    private Version version;
    private Body body;
    
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Space {
        private String id;
        private String key;
        private String name;
        private String type;
        
        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getKey() { return key; }
        public void setKey(String key) { this.key = key; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
    }
    
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Version {
        private int number;
        private String when;
        private String message;
        
        // Get<PERSON> and Setters
        public int getNumber() { return number; }
        public void setNumber(int number) { this.number = number; }
        public String getWhen() { return when; }
        public void setWhen(String when) { this.when = when; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
    
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Body {
        private Storage storage;
        private View view;
        
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Storage {
            private String value;
            private String representation;
            
            // Getters and Setters
            public String getValue() { return value; }
            public void setValue(String value) { this.value = value; }
            public String getRepresentation() { return representation; }
            public void setRepresentation(String representation) { this.representation = representation; }
        }
        
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class View {
            private String value;
            private String representation;
            
            // Getters and Setters
            public String getValue() { return value; }
            public void setValue(String value) { this.value = value; }
            public String getRepresentation() { return representation; }
            public void setRepresentation(String representation) { this.representation = representation; }
        }
        
        // Getters and Setters
        public Storage getStorage() { return storage; }
        public void setStorage(Storage storage) { this.storage = storage; }
        public View getView() { return view; }
        public void setView(View view) { this.view = view; }
    }
    
    // Main class Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    public Space getSpace() { return space; }
    public void setSpace(Space space) { this.space = space; }
    public Version getVersion() { return version; }
    public void setVersion(Version version) { this.version = version; }
    public Body getBody() { return body; }
    public void setBody(Body body) { this.body = body; }
}
