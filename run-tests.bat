@echo off
REM Confluence MCP Server测试运行脚本

echo ========================================
echo Confluence MCP Server 测试运行
echo ========================================

REM 设置测试环境变量
set CONFLUENCE_USERNAME=test_user
set CONFLUENCE_PASSWORD=test_password

echo 正在编译项目...
call mvn clean compile test-compile

if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo.
echo 正在运行测试...
call mvn test -Dspring.profiles.active=test

if %ERRORLEVEL% neq 0 (
    echo 测试失败！
) else (
    echo 测试成功完成！
)

echo.
echo 查看测试报告: target/surefire-reports/
echo 查看测试日志: target/confluence-mcp-server-test.log

pause
