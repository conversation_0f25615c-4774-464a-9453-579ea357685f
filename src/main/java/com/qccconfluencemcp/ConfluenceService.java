package com.qccconfluencemcp;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Confluence服务类
 * 
 * 提供与Confluence API交互的工具方法
 */
@Service
public class ConfluenceService {

    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    public ConfluenceService(WebClient confluenceWebClient, ObjectMapper objectMapper) {
        this.webClient = confluenceWebClient;
        this.objectMapper = objectMapper;
    }

    /**
     * 搜索Confluence页面
     * 
     * @param query 搜索关键词
     * @param limit 返回结果数量限制，默认10
     * @return 搜索结果的JSON字符串
     */
    @Tool(description = "搜索Confluence页面内容，支持关键词搜索")
    public String searchPages(String query, int limit) {
        if (limit <= 0) {
            limit = 10;
        }
        if (limit > 50) {
            limit = 50; // 限制最大返回数量
        }

        try {
            int finalLimit = limit;
            String response = webClient.get()
                    .uri(uriBuilder -> uriBuilder
                            .path("/rest/api/content/search")
                            .queryParam("cql", "text ~ \"" + query + "\" and type = page")
                            .queryParam("limit", finalLimit)
                            .queryParam("expand", "space,version,body.view")
                            .build())
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            return formatSearchResults(response);
        } catch (Exception e) {
            return "搜索失败: " + e.getMessage();
        }
    }

    /**
     * 根据页面ID获取页面详细内容
     * 
     * @param pageId 页面ID
     * @return 页面内容的JSON字符串
     */
    @Tool(description = "根据页面ID获取Confluence页面的详细内容")
    public String getPageContent(String pageId) {
        try {
            String response = webClient.get()
                    .uri(uriBuilder -> uriBuilder
                            .path("/rest/api/content/{pageId}")
                            .queryParam("expand", "space,version,body.storage,body.view,ancestors")
                            .build(pageId))
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            return formatPageContent(response);
        } catch (Exception e) {
            return "获取页面内容失败: " + e.getMessage();
        }
    }

    /**
     * 获取空间信息
     * 
     * @param spaceKey 空间键值
     * @return 空间信息的JSON字符串
     */
    @Tool(description = "获取Confluence空间的详细信息")
    public String getSpaceInfo(String spaceKey) {
        try {
            String response = webClient.get()
                    .uri(uriBuilder -> uriBuilder
                            .path("/rest/api/space/{spaceKey}")
                            .queryParam("expand", "description.plain,homepage")
                            .build(spaceKey))
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            return formatSpaceInfo(response);
        } catch (Exception e) {
            return "获取空间信息失败: " + e.getMessage();
        }
    }

    /**
     * 获取空间下的所有页面
     * 
     * @param spaceKey 空间键值
     * @param limit 返回结果数量限制，默认25
     * @return 页面列表的JSON字符串
     */
    @Tool(description = "获取指定空间下的所有页面列表")
    public String getSpacePages(String spaceKey, int limit) {
        if (limit <= 0) {
            limit = 25;
        }
        if (limit > 100) {
            limit = 100;
        }

        try {
            int finalLimit = limit;
            String response = webClient.get()
                    .uri(uriBuilder -> uriBuilder
                            .path("/rest/api/content")
                            .queryParam("spaceKey", spaceKey)
                            .queryParam("type", "page")
                            .queryParam("limit", finalLimit)
                            .queryParam("expand", "space,version,ancestors")
                            .build())
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            return formatPageList(response);
        } catch (Exception e) {
            return "获取空间页面列表失败: " + e.getMessage();
        }
    }

    /**
     * 格式化搜索结果
     */
    private String formatSearchResults(String jsonResponse) {
        try {
            JsonNode root = objectMapper.readTree(jsonResponse);
            JsonNode results = root.get("results");
            
            List<Map<String, Object>> formattedResults = new ArrayList<>();
            
            if (results != null && results.isArray()) {
                for (JsonNode result : results) {
                    Map<String, Object> item = Map.of(
                        "id", result.get("id").asText(),
                        "title", result.get("title").asText(),
                        "type", result.get("type").asText(),
                        "space", result.get("space").get("name").asText(),
                        "spaceKey", result.get("space").get("key").asText(),
                        "url", result.get("_links").get("webui").asText(),
                        "lastModified", result.get("version").get("when").asText()
                    );
                    formattedResults.add(item);
                }
            }
            
            return objectMapper.writeValueAsString(Map.of(
                "total", root.get("size").asInt(),
                "results", formattedResults
            ));
        } catch (Exception e) {
            return jsonResponse; // 如果格式化失败，返回原始响应
        }
    }

    /**
     * 格式化页面内容
     */
    private String formatPageContent(String jsonResponse) {
        try {
            JsonNode root = objectMapper.readTree(jsonResponse);
            
            Map<String, Object> formattedContent = Map.of(
                "id", root.get("id").asText(),
                "title", root.get("title").asText(),
                "type", root.get("type").asText(),
                "space", Map.of(
                    "name", root.get("space").get("name").asText(),
                    "key", root.get("space").get("key").asText()
                ),
                "version", Map.of(
                    "number", root.get("version").get("number").asInt(),
                    "when", root.get("version").get("when").asText(),
                    "by", root.get("version").get("by").get("displayName").asText()
                ),
                "content", root.has("body") && root.get("body").has("view") ? 
                    root.get("body").get("view").get("value").asText() : "无内容",
                "url", root.get("_links").get("webui").asText()
            );
            
            return objectMapper.writeValueAsString(formattedContent);
        } catch (Exception e) {
            return jsonResponse;
        }
    }

    /**
     * 格式化空间信息
     */
    private String formatSpaceInfo(String jsonResponse) {
        try {
            JsonNode root = objectMapper.readTree(jsonResponse);
            
            Map<String, Object> formattedSpace = Map.of(
                "key", root.get("key").asText(),
                "name", root.get("name").asText(),
                "type", root.get("type").asText(),
                "description", root.has("description") && root.get("description").has("plain") ? 
                    root.get("description").get("plain").get("value").asText() : "无描述",
                "homepage", root.has("homepage") ? 
                    Map.of(
                        "id", root.get("homepage").get("id").asText(),
                        "title", root.get("homepage").get("title").asText()
                    ) : null,
                "url", root.get("_links").get("webui").asText()
            );
            
            return objectMapper.writeValueAsString(formattedSpace);
        } catch (Exception e) {
            return jsonResponse;
        }
    }

    /**
     * 格式化页面列表
     */
    private String formatPageList(String jsonResponse) {
        try {
            JsonNode root = objectMapper.readTree(jsonResponse);
            JsonNode results = root.get("results");
            
            List<Map<String, Object>> formattedResults = new ArrayList<>();
            
            if (results != null && results.isArray()) {
                for (JsonNode result : results) {
                    Map<String, Object> item = Map.of(
                        "id", result.get("id").asText(),
                        "title", result.get("title").asText(),
                        "type", result.get("type").asText(),
                        "space", result.get("space").get("name").asText(),
                        "url", result.get("_links").get("webui").asText(),
                        "lastModified", result.get("version").get("when").asText()
                    );
                    formattedResults.add(item);
                }
            }
            
            return objectMapper.writeValueAsString(Map.of(
                "total", root.get("size").asInt(),
                "results", formattedResults
            ));
        } catch (Exception e) {
            return jsonResponse;
        }
    }
}
