package com.qccconfluencemcp;

/**
 * 简单测试类，验证基本功能
 */
public class SimpleTest {
    public static void main(String[] args) {
        System.out.println("Confluence MCP Server - 基本测试");
        System.out.println("Java版本: " + System.getProperty("java.version"));
        System.out.println("工作目录: " + System.getProperty("user.dir"));
        
        // 测试基本的Confluence服务逻辑
        try {
            System.out.println("测试成功：应用可以正常启动");
            
            // 模拟MCP工具调用
            System.out.println("模拟工具调用:");
            System.out.println("- searchPages: 搜索Confluence页面");
            System.out.println("- getPageContent: 获取页面内容");
            System.out.println("- getSpaceInfo: 获取空间信息");
            System.out.println("- getSpacePages: 获取空间页面列表");
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
