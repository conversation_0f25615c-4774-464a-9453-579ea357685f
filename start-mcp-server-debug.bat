@echo off
REM Confluence MCP Server调试启动脚本

echo ========================================
echo Confluence MCP Server 调试模式启动
echo ========================================

REM 设置Confluence连接信息（请根据实际情况修改）
set CONFLUENCE_USERNAME=your_username
set CONFLUENCE_PASSWORD=your_password

echo 正在启动MCP服务器（调试模式）...
echo 使用配置文件: application-dev.yml
echo.

REM 启动MCP服务器（开发模式，带详细日志）
java -Dspring.profiles.active=dev ^
     -Dspring.ai.mcp.server.transport=STDIO ^
     -Dspring.main.web-application-type=none ^
     -DCONFLUENCE_USERNAME=%CONFLUENCE_USERNAME% ^
     -DCONFLUENCE_PASSWORD=%CONFLUENCE_PASSWORD% ^
     -jar target/qccConfluenceMcp-0.0.1-SNAPSHOT.jar

echo.
echo MCP服务器已停止
pause
