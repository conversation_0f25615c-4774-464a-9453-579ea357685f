#!/bin/bash

# Confluence MCP Server启动脚本

# 设置Confluence连接信息（请根据实际情况修改）
export CONFLUENCE_USERNAME=${CONFLUENCE_USERNAME:-"your_username"}
export CONFLUENCE_PASSWORD=${CONFLUENCE_PASSWORD:-"your_password"}

# 启动MCP服务器（STDIO模式）
java -Dspring.ai.mcp.server.transport=STDIO \
     -Dspring.main.web-application-type=none \
     -Dlogging.pattern.console= \
     -DCONFLUENCE_USERNAME="$CONFLUENCE_USERNAME" \
     -DCONFLUENCE_PASSWORD="$CONFLUENCE_PASSWORD" \
     -jar target/qccConfluenceMcp-0.0.1-SNAPSHOT.jar
