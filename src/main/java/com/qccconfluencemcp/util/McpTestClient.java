package com.qccconfluencemcp.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * MCP测试客户端工具
 * 用于测试Confluence API连接和功能
 */
public class McpTestClient {

    public static void main(String[] args) {
        // 从环境变量或命令行参数获取配置
        String baseUrl = System.getProperty("confluence.base-url", "https://doc.greatld.com");
        String username = System.getProperty("confluence.username", System.getenv("CONFLUENCE_USERNAME"));
        String password = System.getProperty("confluence.password", System.getenv("CONFLUENCE_PASSWORD"));

        if (username == null || password == null) {
            System.err.println("请设置CONFLUENCE_USERNAME和CONFLUENCE_PASSWORD环境变量");
            System.exit(1);
        }

        System.out.println("测试Confluence连接...");
        System.out.println("服务器: " + baseUrl);
        System.out.println("用户: " + username);

        // 创建WebClient
        WebClient webClient = WebClient.builder()
                .baseUrl(baseUrl)
                .defaultHeader("Authorization", "Basic " + 
                    java.util.Base64.getEncoder().encodeToString((username + ":" + password).getBytes()))
                .build();

        try {
            // 测试连接 - 获取当前用户信息
            String userInfo = webClient.get()
                    .uri("/rest/api/user/current")
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            System.out.println("连接成功！");
            System.out.println("用户信息: " + userInfo);

            // 测试搜索功能
            System.out.println("\n测试搜索功能...");
            String searchResult = webClient.get()
                    .uri(uriBuilder -> uriBuilder
                            .path("/rest/api/content/search")
                            .queryParam("cql", "text ~ \"Spring\" and type = page")
                            .queryParam("limit", 5)
                            .queryParam("expand", "space,version")
                            .build())
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            System.out.println("搜索结果: " + searchResult);

        } catch (Exception e) {
            System.err.println("连接失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
