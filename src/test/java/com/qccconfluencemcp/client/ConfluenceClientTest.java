package com.qccconfluencemcp.client;

import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.client.transport.ServerParameters;
import io.modelcontextprotocol.client.transport.StdioClientTransport;
import io.modelcontextprotocol.spec.McpSchema;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * Confluence MCP客户端测试
 */
public class ConfluenceClientTest {

    private static final Logger logger = LoggerFactory.getLogger(ConfluenceClientTest.class);

    @Test
    public void testConfluenceTools() throws Exception {
        // 创建服务器参数
        ServerParameters stdioParams = ServerParameters.builder("java")
                .args(
                    "-Dspring.ai.mcp.server.transport=STDIO",
                    "-Dspring.main.web-application-type=none",
                    "-Dlogging.pattern.console=",
                    "-jar",
                    "target/qccConfluenceMcp-0.0.1-SNAPSHOT.jar"
                )
                .build();

        // 初始化传输和客户端
        var transport = new StdioClientTransport(stdioParams);
        var client = McpClient.sync(transport).build();

        try {
            // 初始化客户端
            client.initialize();
            
            // 获取可用工具列表
            var tools = client.listTools();
            logger.info("可用工具: {}", tools);

            // 测试搜索页面工具
            McpSchema.CallToolResult searchResult = client.callTool(
                new McpSchema.CallToolRequest("searchPages", Map.of(
                    "query", "Spring",
                    "limit", 5
                ))
            );
            logger.info("搜索结果: {}", searchResult);

            // 测试获取空间信息工具
            McpSchema.CallToolResult spaceResult = client.callTool(
                new McpSchema.CallToolRequest("getSpaceInfo", Map.of(
                    "spaceKey", "DEMO"
                ))
            );
            logger.info("空间信息: {}", spaceResult);

        } finally {
            client.close();
        }
    }
}
