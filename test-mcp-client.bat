@echo off
REM 简单的MCP客户端测试脚本

echo ========================================
echo MCP客户端测试
echo ========================================

REM 设置环境变量
set CONFLUENCE_USERNAME=your_username
set CONFLUENCE_PASSWORD=your_password

echo 正在启动MCP服务器进行测试...

REM 启动MCP服务器并进行简单的交互测试
echo {"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {"tools": {}}}} | ^
java -Dspring.profiles.active=dev ^
     -Dspring.ai.mcp.server.transport=STDIO ^
     -Dspring.main.web-application-type=none ^
     -DCONFLUENCE_USERNAME=%CONFLUENCE_USERNAME% ^
     -DCONFLUENCE_PASSWORD=%CONFLUENCE_PASSWORD% ^
     -jar target/qccConfluenceMcp-0.0.1-SNAPSHOT.jar

pause
