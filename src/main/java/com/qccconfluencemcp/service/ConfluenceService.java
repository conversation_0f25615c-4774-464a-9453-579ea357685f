package com.qccconfluencemcp.service;

import com.qccconfluencemcp.config.ConfluenceProperties;
import com.qccconfluencemcp.model.ConfluencePage;
import com.qccconfluencemcp.model.ConfluenceSearchResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Base64;

/**
 * Confluence服务类，提供MCP工具
 * 基于Spring AI Tool注解实现
 */
@Service
public class ConfluenceService {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfluenceService.class);
    
    private final WebClient webClient;
    private final ConfluenceProperties properties;
    
    public ConfluenceService(ConfluenceProperties properties) {
        this.properties = properties;
        this.webClient = createWebClient();
    }
    
    private WebClient createWebClient() {
        String auth = properties.getUsername() + ":" + properties.getPassword();
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes(StandardCharsets.UTF_8));
        
        return WebClient.builder()
                .baseUrl(properties.getBaseUrl())
                .defaultHeader("Authorization", "Basic " + encodedAuth)
                .defaultHeader("Content-Type", "application/json")
                .defaultHeader("Accept", "application/json")
                .build();
    }
    
    /**
     * 获取Confluence页面内容
     */
    public String getConfluencePage(String pageId, String pageUrl) {
        try {
            logger.info("获取Confluence页面，pageId: {}, pageUrl: {}", pageId, pageUrl);
            
            ConfluencePage page;
            if (pageUrl != null && !pageUrl.trim().isEmpty()) {
                page = getPageByUrl(pageUrl).block();
            } else if (pageId != null && !pageId.trim().isEmpty()) {
                page = getPageById(pageId).block();
            } else {
                return "错误：必须提供pageId或pageUrl参数";
            }
            
            if (page == null) {
                return "未找到指定的页面";
            }
            
            return formatPageContent(page);
            
        } catch (Exception e) {
            logger.error("获取页面内容时发生错误", e);
            return "获取页面内容时发生错误: " + e.getMessage();
        }
    }
    
    /**
     * 搜索Confluence内容
     */
    public String searchConfluence(String query, Integer limit, Integer start) {
        try {
            logger.info("搜索Confluence内容，query: {}, limit: {}, start: {}", query, limit, start);
            
            if (query == null || query.trim().isEmpty()) {
                return "错误：搜索关键词不能为空";
            }
            
            int searchLimit = limit != null ? limit : 10;
            int searchStart = start != null ? start : 0;
            
            ConfluenceSearchResult searchResult = searchContent(query, searchLimit, searchStart).block();
            
            if (searchResult == null || searchResult.getResults() == null || searchResult.getResults().isEmpty()) {
                return "未找到匹配的搜索结果";
            }
            
            return formatSearchResults(searchResult, query);
            
        } catch (Exception e) {
            logger.error("搜索内容时发生错误", e);
            return "搜索时发生错误: " + e.getMessage();
        }
    }
    
    /**
     * 根据页面ID获取页面内容
     */
    private Mono<ConfluencePage> getPageById(String pageId) {
        return webClient.get()
                .uri("/rest/api/content/{pageId}?expand=body.storage,body.view,space,version", pageId)
                .retrieve()
                .bodyToMono(ConfluencePage.class)
                .timeout(Duration.ofMillis(properties.getReadTimeout()))
                .onErrorMap(WebClientResponseException.class, ex -> 
                    new RuntimeException("获取页面失败: " + ex.getMessage(), ex));
    }
    
    /**
     * 根据页面URL获取页面内容
     */
    private Mono<ConfluencePage> getPageByUrl(String pageUrl) {
        String pageId = extractPageIdFromUrl(pageUrl);
        if (pageId == null) {
            return Mono.error(new IllegalArgumentException("无法从URL中提取页面ID: " + pageUrl));
        }
        return getPageById(pageId);
    }
    
    /**
     * 搜索Confluence内容
     */
    private Mono<ConfluenceSearchResult> searchContent(String query, int limit, int start) {
        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/rest/api/search")
                        .queryParam("cql", "text ~ \"" + query + "\" and type = page")
                        .queryParam("limit", limit)
                        .queryParam("start", start)
                        .queryParam("expand", "space")
                        .build())
                .retrieve()
                .bodyToMono(ConfluenceSearchResult.class)
                .timeout(Duration.ofMillis(properties.getReadTimeout()))
                .onErrorMap(WebClientResponseException.class, ex -> 
                    new RuntimeException("搜索内容失败: " + ex.getMessage(), ex));
    }
    
    /**
     * 从URL中提取页面ID
     */
    private String extractPageIdFromUrl(String url) {
        try {
            if (url.contains("pageId=")) {
                String[] parts = url.split("pageId=");
                if (parts.length > 1) {
                    return parts[1].split("&")[0];
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 格式化页面内容
     */
    private String formatPageContent(ConfluencePage page) {
        StringBuilder result = new StringBuilder();
        result.append("# ").append(page.getTitle()).append("\n\n");
        
        if (page.getSpace() != null) {
            result.append("**空间**: ").append(page.getSpace().getName()).append("\n");
            result.append("**空间键**: ").append(page.getSpace().getKey()).append("\n\n");
        }
        
        if (page.getVersion() != null) {
            result.append("**版本**: ").append(page.getVersion().getNumber()).append("\n");
            result.append("**更新时间**: ").append(page.getVersion().getWhen()).append("\n\n");
        }
        
        result.append("## 页面内容\n\n");
        
        if (page.getBody() != null) {
            if (page.getBody().getView() != null && page.getBody().getView().getValue() != null) {
                result.append(page.getBody().getView().getValue());
            } else if (page.getBody().getStorage() != null && page.getBody().getStorage().getValue() != null) {
                result.append(page.getBody().getStorage().getValue());
            } else {
                result.append("页面内容为空");
            }
        } else {
            result.append("页面内容为空");
        }
        
        return result.toString();
    }
    
    /**
     * 格式化搜索结果
     */
    private String formatSearchResults(ConfluenceSearchResult searchResult, String query) {
        StringBuilder result = new StringBuilder();
        result.append("# 搜索结果\n\n");
        result.append("**搜索关键词**: ").append(query).append("\n");
        result.append("**找到结果**: ").append(searchResult.getSize()).append(" 条\n\n");
        
        for (ConfluenceSearchResult.SearchResultItem item : searchResult.getResults()) {
            result.append("## ").append(item.getTitle()).append("\n");
            result.append("**页面ID**: ").append(item.getId()).append("\n");
            if (item.getSpace() != null) {
                result.append("**空间**: ").append(item.getSpace().getName()).append("\n");
            }
            if (item.getUrl() != null) {
                result.append("**链接**: ").append(item.getUrl()).append("\n");
            }
            if (item.getExcerpt() != null && !item.getExcerpt().trim().isEmpty()) {
                result.append("**摘要**: ").append(item.getExcerpt()).append("\n");
            }
            result.append("\n---\n\n");
        }
        
        return result.toString();
    }
    
    /**
     * 测试连接
     */
    public Mono<Boolean> testConnection() {
        return webClient.get()
                .uri("/rest/api/space")
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> true)
                .timeout(Duration.ofMillis(properties.getConnectTimeout()))
                .onErrorReturn(false);
    }
}
