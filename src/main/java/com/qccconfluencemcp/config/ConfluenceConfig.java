package com.qccconfluencemcp.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;
import io.netty.channel.ChannelOption;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;

import javax.net.ssl.SSLException;
import java.time.Duration;
import java.util.Base64;

/**
 * Confluence配置类
 */
@Configuration
public class ConfluenceConfig {

    /**
     * 配置WebClient用于Confluence API调用
     * 
     * @param properties Confluence配置属性
     * @return WebClient实例
     */
    @Bean
    public WebClient confluenceWebClient(ConfluenceProperties properties) {
        // 配置连接池
        ConnectionProvider connectionProvider = ConnectionProvider.builder("confluence")
                .maxConnections(50)
                .maxIdleTime(Duration.ofSeconds(20))
                .maxLifeTime(Duration.ofSeconds(60))
                .pendingAcquireTimeout(Duration.ofSeconds(60))
                .evictInBackground(Duration.ofSeconds(120))
                .build();

        // 配置HttpClient
        HttpClient httpClient = HttpClient.create(connectionProvider)
                .responseTimeout(Duration.ofMillis(properties.getReadTimeout()))
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, properties.getConnectTimeout());

        // 如果禁用SSL验证
        if (!properties.isSslVerification()) {
            try {
                httpClient = httpClient.secure(spec -> {
                    try {
                        spec.sslContext(
                            SslContextBuilder.forClient()
                                .trustManager(InsecureTrustManagerFactory.INSTANCE)
                                .build()
                        );
                    } catch (SSLException e) {
                        throw new RuntimeException(e);
                    }
                });
            } catch (Exception e) {
                throw new RuntimeException("SSL配置失败", e);
            }
        }

        WebClient.Builder builder = WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .baseUrl(properties.getBaseUrl());

        // 添加基础认证
        if (properties.getUsername() != null && properties.getPassword() != null) {
            String auth = properties.getUsername() + ":" + properties.getPassword();
            String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
            builder.defaultHeader("Authorization", "Basic " + encodedAuth);
        }

        return builder.build();
    }
}
