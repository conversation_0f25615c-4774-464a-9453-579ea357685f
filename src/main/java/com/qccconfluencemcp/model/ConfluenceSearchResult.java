package com.qccconfluencemcp.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * Confluence搜索结果模型
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConfluenceSearchResult {
    
    private List<SearchResultItem> results;
    private int start;
    private int limit;
    private int size;
    
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SearchResultItem {
        private String id;
        private String type;
        private String status;
        private String title;
        private String url;
        private Space space;
        private String excerpt;
        
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Space {
            private String id;
            private String key;
            private String name;
            
            // Getters and Setters
            public String getId() { return id; }
            public void setId(String id) { this.id = id; }
            public String getKey() { return key; }
            public void setKey(String key) { this.key = key; }
            public String getName() { return name; }
            public void setName(String name) { this.name = name; }
        }
        
        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
        public Space getSpace() { return space; }
        public void setSpace(Space space) { this.space = space; }
        public String getExcerpt() { return excerpt; }
        public void setExcerpt(String excerpt) { this.excerpt = excerpt; }
    }
    
    // Main class Getters and Setters
    public List<SearchResultItem> getResults() { return results; }
    public void setResults(List<SearchResultItem> results) { this.results = results; }
    public int getStart() { return start; }
    public void setStart(int start) { this.start = start; }
    public int getLimit() { return limit; }
    public void setLimit(int limit) { this.limit = limit; }
    public int getSize() { return size; }
    public void setSize(int size) { this.size = size; }
}
